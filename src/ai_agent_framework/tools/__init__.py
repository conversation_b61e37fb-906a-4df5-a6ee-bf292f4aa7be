"""
AI Agent Framework 工具模块

包含各种工具实现和工具执行系统：
- ToolRegistry: 工具注册表（已在utils中实现）
- 示例工具实现
- 工具执行器
"""

from ai_agent_framework.tools.example_tools import (
    CalculatorTool,
    SearchTool,
    WeatherTool,
)
from ai_agent_framework.tools.crawler_tool import (
    CrawlerTool,
    CrawlerServiceType,
)
from ai_agent_framework.tools.database_tool import DatabaseTool
from ai_agent_framework.tools.git_tool import GitTool
from ai_agent_framework.tools.http_client_tool import HTTPClientTool
from ai_agent_framework.tools.message_queue_tool import (
    MessageQueueTool,
    MessageQueueType,
    MessagePriority,
)
from ai_agent_framework.tools.cache_tool import (
    CacheTool,
    CacheType,
)
from ai_agent_framework.tools.file_storage_tool import (
    FileStorageTool,
    StorageType,
)
from ai_agent_framework.tools.data_processing_tool import DataProcessingTool
from ai_agent_framework.tools.oauth2_tool import OA<PERSON>2T<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>2P<PERSON>ider, OAuth2GrantType
from ai_agent_framework.utils.tool_registry import ToolRegistry

__all__ = [
    # 基础示例工具
    "CalculatorTool",
    "WeatherTool",
    "SearchTool",
    # 爬虫工具
    "CrawlerTool",
    "CrawlerServiceType",
    # 数据库工具
    "DatabaseTool",
    # Git工具
    "GitTool",
    # HTTP客户端工具
    "HTTPClientTool",
    # 消息队列工具
    "MessageQueueTool",
    "MessageQueueType",
    "MessagePriority",
    # 缓存工具
    "CacheTool",
    "CacheType",
    # 文件存储工具
    "FileStorageTool",
    "StorageType",
    # 数据处理工具
    "DataProcessingTool",
    # OAuth2认证工具
    "OAuth2Tool",
    "OAuth2Config",
    "OAuth2Token",
    "OAuth2Provider",
    "OAuth2GrantType",
    # 工具注册表
    "ToolRegistry",
]
